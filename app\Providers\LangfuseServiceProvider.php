<?php

namespace App\Providers;

use App\Services\Langfuse\LangfuseService;
use App\Services\Langfuse\LangfuseTrackingService;
use Illuminate\Support\ServiceProvider;

class LangfuseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the main Langfuse service as a deferred singleton
        $this->app->singleton(LangfuseService::class, function ($app) {
            return new LangfuseService();
        });

        // Register the tracking service
        $this->app->singleton(LangfuseTrackingService::class, function ($app) {
            return new LangfuseTrackingService($app->make(LangfuseService::class));
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/langfuse.php' => config_path('langfuse.php'),
            ], 'langfuse-config');
        }
    }
}
