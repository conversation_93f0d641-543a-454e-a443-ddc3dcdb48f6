<?php

namespace App\Services\Langfuse;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LangfuseService
{
    protected Client $client;
    protected array $config;
    protected bool $enabled;

    public function __construct()
    {
        $this->config = config('langfuse') ?? [];
        $this->enabled = $this->config['enabled'] ?? false;

        if ($this->enabled && $this->hasRequiredConfig()) {
            $clientConfig = [
                'base_uri' => rtrim($this->config['host'], '/') . '/api/public/',
                'timeout' => $this->config['timeout'],
                'connect_timeout' => $this->config['connect_timeout'],
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Basic ' . base64_encode(
                        $this->config['public_key'] . ':' . $this->config['secret_key']
                    ),
                ],
            ];

            // Handle SSL verification for local development
            if (app()->environment('local', 'testing')) {
                $clientConfig['verify'] = false;
            }

            $this->client = new Client($clientConfig);
        }
    }

    /**
     * Check if required configuration is available
     */
    protected function hasRequiredConfig(): bool
    {
        return !empty($this->config['public_key']) &&
               !empty($this->config['secret_key']) &&
               !empty($this->config['host']);
    }

    /**
     * Check if Langfuse is enabled and properly configured
     */
    public function isEnabled(): bool
    {
        return $this->enabled && $this->hasRequiredConfig();
    }

    /**
     * Create a new trace
     */
    public function createTrace(array $data): ?string
    {
        if (!$this->isEnabled()) {
            return null;
        }

        try {
            $traceId = $data['id'] ?? Str::uuid()->toString();

            $payload = [
                'id' => $traceId,
                'name' => $data['name'] ?? 'Chat Interaction',
                'userId' => $data['userId'] ?? null,
                'sessionId' => $data['sessionId'] ?? null,
                'metadata' => $data['metadata'] ?? [],
                'tags' => $data['tags'] ?? [],
                'timestamp' => $data['timestamp'] ?? now()->toISOString(),
            ];

            $this->makeRequest('POST', 'traces', $payload);

            return $traceId;
        } catch (\Exception $e) {
            $this->handleError('Failed to create trace', $e);
            return null;
        }
    }

    /**
     * Create a generation (LLM call) within a trace
     */
    public function createGeneration(array $data): ?string
    {
        if (!$this->isEnabled()) {
            return null;
        }

        try {
            $generationId = $data['id'] ?? Str::uuid()->toString();

            $payload = [
                'id' => $generationId,
                'traceId' => $data['traceId'],
                'name' => $data['name'] ?? 'LLM Generation',
                'model' => $data['model'] ?? null,
                'input' => $this->truncateContent($data['input'] ?? null),
                'output' => $this->truncateContent($data['output'] ?? null),
                'usage' => $data['usage'] ?? null,
                'metadata' => $data['metadata'] ?? [],
                'startTime' => $data['startTime'] ?? now()->toISOString(),
                'endTime' => $data['endTime'] ?? now()->toISOString(),
            ];

            $this->makeRequest('POST', 'generations', $payload);

            return $generationId;
        } catch (\Exception $e) {
            $this->handleError('Failed to create generation', $e);
            return null;
        }
    }

    /**
     * Create a span (for non-LLM operations) within a trace
     */
    public function createSpan(array $data): ?string
    {
        if (!$this->isEnabled()) {
            return null;
        }

        try {
            $spanId = $data['id'] ?? Str::uuid()->toString();

            $payload = [
                'id' => $spanId,
                'traceId' => $data['traceId'],
                'name' => $data['name'],
                'input' => $this->truncateContent($data['input'] ?? null),
                'output' => $this->truncateContent($data['output'] ?? null),
                'metadata' => $data['metadata'] ?? [],
                'startTime' => $data['startTime'] ?? now()->toISOString(),
                'endTime' => $data['endTime'] ?? now()->toISOString(),
            ];

            $this->makeRequest('POST', 'spans', $payload);

            return $spanId;
        } catch (\Exception $e) {
            $this->handleError('Failed to create span', $e);
            return null;
        }
    }

    /**
     * Create or update a session
     */
    public function createSession(array $data): ?string
    {
        if (!$this->isEnabled()) {
            return null;
        }

        try {
            $sessionId = $data['id'] ?? Str::uuid()->toString();

            $payload = [
                'id' => $sessionId,
                'userId' => $data['userId'] ?? null,
                'metadata' => $data['metadata'] ?? [],
            ];

            $this->makeRequest('POST', 'sessions', $payload);

            return $sessionId;
        } catch (\Exception $e) {
            $this->handleError('Failed to create session', $e);
            return null;
        }
    }

    /**
     * Add a score to a trace or generation
     */
    public function createScore(array $data): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        try {
            $payload = [
                'id' => $data['id'] ?? Str::uuid()->toString(),
                'traceId' => $data['traceId'],
                'observationId' => $data['observationId'] ?? null,
                'name' => $data['name'],
                'value' => $data['value'],
                'comment' => $data['comment'] ?? null,
                'metadata' => $data['metadata'] ?? [],
            ];

            $this->makeRequest('POST', 'scores', $payload);

            return true;
        } catch (\Exception $e) {
            $this->handleError('Failed to create score', $e);
            return false;
        }
    }

    /**
     * Make HTTP request to Langfuse API
     */
    protected function makeRequest(string $method, string $endpoint, array $data): array
    {
        $response = $this->client->request($method, $endpoint, [
            'json' => $data,
        ]);

        return json_decode($response->getBody()->getContents(), true) ?? [];
    }

    /**
     * Truncate content to prevent huge payloads
     */
    protected function truncateContent($content): mixed
    {
        if (!is_string($content)) {
            return $content;
        }

        $maxLength = $this->config['tracing']['max_content_length'] ?? 10000;

        if (strlen($content) > $maxLength) {
            return substr($content, 0, $maxLength) . '... [truncated]';
        }

        return $content;
    }

    /**
     * Handle errors based on configuration
     */
    protected function handleError(string $message, \Exception $e): void
    {
        if ($this->config['error_handling']['log_errors'] ?? true) {
            $logLevel = $this->config['error_handling']['log_level'] ?? 'warning';

            Log::log($logLevel, $message, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        if (!($this->config['error_handling']['fail_silently'] ?? true)) {
            throw $e;
        }
    }
}
