<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Langfuse Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Langfuse LLM observability and analytics platform.
    | Langfuse provides tracing, monitoring, and analytics for LLM applications.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Langfuse API Configuration
    |--------------------------------------------------------------------------
    |
    | These settings configure the connection to your Langfuse instance.
    | You can use either Langfuse Cloud or a self-hosted instance.
    |
    */

    'enabled' => env('LANGFUSE_ENABLED'),

    'public_key' => env('LANGFUSE_PUBLIC_KEY'),
    'secret_key' => env('LANGFUSE_SECRET_KEY'),
    'host' => env('LANGFUSE_HOST', 'https://cloud.langfuse.com'),

    /*
    |--------------------------------------------------------------------------
    | Request Configuration
    |--------------------------------------------------------------------------
    |
    | Configure HTTP client settings for API requests to Langfuse.
    |
    */

    'timeout' => env('LANGFUSE_TIMEOUT', 30),
    'connect_timeout' => env('LANGFUSE_CONNECT_TIMEOUT', 10),

    /*
    |--------------------------------------------------------------------------
    | Tracing Configuration
    |--------------------------------------------------------------------------
    |
    | Configure what data to include in traces and how to handle them.
    |
    */

    'tracing' => [
        // Whether to include user information in traces
        'include_user_data' => env('LANGFUSE_INCLUDE_USER_DATA', true),
        
        // Whether to include request metadata
        'include_metadata' => env('LANGFUSE_INCLUDE_METADATA', true),
        
        // Whether to trace in development environment
        'trace_in_development' => env('LANGFUSE_TRACE_IN_DEVELOPMENT', true),
        
        // Whether to trace in testing environment
        'trace_in_testing' => env('LANGFUSE_TRACE_IN_TESTING', false),
        
        // Maximum length for input/output content (to prevent huge payloads)
        'max_content_length' => env('LANGFUSE_MAX_CONTENT_LENGTH', 10000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Configuration
    |--------------------------------------------------------------------------
    |
    | Configure how chat sessions are tracked in Langfuse.
    |
    */

    'sessions' => [
        // Whether to automatically create sessions for chat conversations
        'auto_create' => env('LANGFUSE_AUTO_CREATE_SESSIONS', true),
        
        // Session metadata to include
        'include_agent_info' => env('LANGFUSE_INCLUDE_AGENT_INFO', true),
        'include_user_info' => env('LANGFUSE_INCLUDE_USER_INFO', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    |
    | Configure how to handle errors when communicating with Langfuse.
    |
    */

    'error_handling' => [
        // Whether to fail silently on Langfuse errors (recommended for production)
        'fail_silently' => env('LANGFUSE_FAIL_SILENTLY', true),
        
        // Whether to log errors
        'log_errors' => env('LANGFUSE_LOG_ERRORS', true),
        
        // Log level for errors
        'log_level' => env('LANGFUSE_LOG_LEVEL', 'warning'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Batching Configuration
    |--------------------------------------------------------------------------
    |
    | Configure batching of requests to improve performance.
    |
    */

    'batching' => [
        // Whether to enable batching (recommended for high-volume applications)
        'enabled' => env('LANGFUSE_BATCHING_ENABLED', false),
        
        // Maximum batch size
        'max_batch_size' => env('LANGFUSE_MAX_BATCH_SIZE', 100),
        
        // Maximum time to wait before sending a batch (seconds)
        'max_wait_time' => env('LANGFUSE_MAX_WAIT_TIME', 5),
    ],

];
