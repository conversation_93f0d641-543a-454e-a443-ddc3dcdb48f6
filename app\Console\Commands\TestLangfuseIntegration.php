<?php

namespace App\Console\Commands;

use App\Services\Langfuse\LangfuseService;
use App\Services\Langfuse\LangfuseTrackingService;
use Illuminate\Console\Command;

class TestLangfuseIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'langfuse:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Langfuse integration and connectivity';

    protected LangfuseService $langfuse;
    protected LangfuseTrackingService $tracking;

    public function __construct(LangfuseService $langfuse, LangfuseTrackingService $tracking)
    {
        parent::__construct();
        $this->langfuse = $langfuse;
        $this->tracking = $tracking;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Langfuse Integration...');
        $this->newLine();

        // Check configuration
        $this->checkConfiguration();
        $this->newLine();

        // Test connectivity
        if ($this->langfuse->isEnabled()) {
            $this->testConnectivity();
        } else {
            $this->warn('Lang<PERSON> is disabled or not properly configured. Skipping connectivity test.');
        }

        $this->newLine();
        $this->info('Langfuse integration test completed.');
    }

    protected function checkConfiguration(): void
    {
        $this->info('Checking Configuration:');

        $enabled = config('langfuse.enabled', true);
        $publicKey = config('langfuse.public_key', 'pk-lf-3675729a-1e70-4063-a050-8ba19d6aaed9');
        $secretKey = config('langfuse.secret_key', '******************************************');
        $host = config('langfuse.host', 'https://cloud.langfuse.com');

        $this->line("  Enabled: " . ($enabled ? '✅ Yes' : '❌ No'));
        $this->line("  Host: " . ($host ?: '❌ Not set'));
        $this->line("  Public Key: " . ($publicKey ? '✅ Set' : '❌ Not set'));
        $this->line("  Secret Key: " . ($secretKey ? '✅ Set' : '❌ Not set'));

        if (!$enabled) {
            $this->warn('  → Set LANGFUSE_ENABLED=true in your .env file');
        }

        if (!$publicKey) {
            $this->warn('  → Set LANGFUSE_PUBLIC_KEY in your .env file');
        }

        if (!$secretKey) {
            $this->warn('  → Set LANGFUSE_SECRET_KEY in your .env file');
        }

        if ($this->langfuse->isEnabled()) {
            $this->info('  ✅ Configuration looks good!');
        } else {
            $this->error('  ❌ Configuration incomplete');
        }
    }

    protected function testConnectivity(): void
    {
        $this->info('Testing Connectivity:');

        try {
            // Create a test trace
            $testData = [
                'name' => 'Langfuse Integration Test',
                'metadata' => [
                    'test' => true,
                    'timestamp' => now()->toISOString(),
                    'command' => 'langfuse:test',
                ],
                'tags' => ['test', 'integration'],
            ];

            $this->line('  Creating test trace...');
            $traceId = $this->langfuse->createTrace($testData);

            if ($traceId) {
                $this->info("  ✅ Successfully created trace: {$traceId}");

                // Create a test generation
                $this->line('  Creating test generation...');
                $generationData = [
                    'traceId' => $traceId,
                    'name' => 'Test Generation',
                    'model' => 'test-model',
                    'input' => 'This is a test prompt',
                    'output' => 'This is a test response',
                    'metadata' => [
                        'test' => true,
                    ],
                ];

                $generationId = $this->langfuse->createGeneration($generationData);

                if ($generationId) {
                    $this->info("  ✅ Successfully created generation: {$generationId}");
                } else {
                    $this->warn('  ⚠️  Failed to create test generation');
                }

                // Create a test score
                $this->line('  Creating test score...');
                $scoreData = [
                    'traceId' => $traceId,
                    'name' => 'test_score',
                    'value' => 1.0,
                    'comment' => 'Integration test score',
                ];

                $scoreResult = $this->langfuse->createScore($scoreData);

                if ($scoreResult) {
                    $this->info('  ✅ Successfully created test score');
                } else {
                    $this->warn('  ⚠️  Failed to create test score');
                }

                $this->newLine();
                $this->info('🎉 Connectivity test successful!');
                $this->line("Check your Langfuse dashboard to see the test trace: {$traceId}");

            } else {
                $this->error('  ❌ Failed to create test trace');
                $this->warn('  Check your API keys and network connectivity');
            }

        } catch (\Exception $e) {
            $this->error('  ❌ Connectivity test failed');
            $this->error("  Error: {$e->getMessage()}");
            
            if ($this->option('verbose')) {
                $this->error("  Trace: {$e->getTraceAsString()}");
            }
        }
    }
}
